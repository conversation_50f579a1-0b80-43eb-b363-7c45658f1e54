"use client"

import { useEffect, useState } from "react"
import { useUserStore } from "./user.store"
import { useUser } from "../auth/auth.hooks"
import { User, UserUpdateData } from "./user.types"
import { UserService } from "./user.service"
import { toast } from "@/components/ui/use-toast"
import {
  uploadBlobAction,
  deleteBlobAction,
} from "@/app/(authenticated)/settings/actions/upload-blob"

// Export real-time hooks
export * from "./user.realtime.hooks"

/**
 * Hook to get a user by ID
 */
export const useUserById = (userId: string, useRealtime: boolean = false) => {
  const { users, loading, error, fetchUser, subscribeToUser, unsubscribeFromUser } = useUserStore()

  useEffect(() => {
    if (userId) {
      if (useRealtime) {
        // Use real-time subscription
        subscribeToUser(userId)

        // Cleanup on unmount
        return () => {
          unsubscribeFromUser()
        }
      } else {
        // Use regular fetch
        fetchUser(userId)
      }
    }

    // Return empty cleanup function for TypeScript
    return () => {}
  }, [userId, fetchUser, subscribeToUser, unsubscribeFromUser, useRealtime])

  return { user: users[userId] || null, loading, error }
}

/**
 * Hook to get the current authenticated user's data
 */
export const useCurrentUser = (useRealtime: boolean = false) => {
  const authUser = useUser()
  const userId = authUser?.uid

  return useUserById(userId || "", useRealtime)
}

/**
 * Hook to get multiple users by their IDs
 * @param userIds Array of user IDs to fetch
 * @returns Object containing users array, loading state, and error
 */
export const useUsersByIds = (userIds: string[]) => {
  const [users, setUsers] = useState<User[]>([])
  const [loading, setLoading] = useState<boolean>(true)
  const [error, setError] = useState<Error | null>(null)

  useEffect(() => {
    const fetchUsers = async () => {
      // Reset states
      setLoading(true)
      setError(null)

      // Handle empty array case
      if (!userIds || userIds.length === 0) {
        setUsers([])
        setLoading(false)
        return
      }

      try {
        // Fetch users using the UserService
        const fetchedUsers = await UserService.getUsersFromIds(userIds)
        setUsers(fetchedUsers)
      } catch (err) {
        console.error("Error fetching users:", err)
        setError(err as Error)
      } finally {
        setLoading(false)
      }
    }

    fetchUsers()
  }, [userIds])

  return { users, loading, error }
}

export const useUpdateUser = () => {
  const { updateUser: updateUserOnStore } = useUserStore()
  const [updating, setUpdating] = useState(false)
  const [error, setError] = useState<Error | null>(null)

  const updateUser = async (userId: string, userData: UserUpdateData) => {
    try {
      setUpdating(true)
      setError(null)
      const success = await updateUserOnStore(userId, userData)
      setUpdating(false)
      return success
    } catch (err) {
      setError(err as Error)
      setUpdating(false)
      return false
    }
  }
  return { updateUser, updating, error }
}

export interface ProfilePictureResult {
  success: boolean
  url?: string
  error?: string
}

/**
 * Hook for managing profile picture uploads using Vercel Blob + Firebase Client SDK
 */
export const useProfilePictureManager = () => {
  const user = useUser()
  const { updateUser } = useUpdateUser()
  const [uploading, setUploading] = useState(false)
  const [removing, setRemoving] = useState(false)

  const uploadProfilePicture = async (file: File): Promise<ProfilePictureResult> => {
    if (!user?.uid) {
      return {
        success: false,
        error: "You must be logged in to upload a profile picture",
      }
    }

    setUploading(true)
    try {
      // Create form data for the server action
      const formData = new FormData()
      formData.append("file", file)

      // Upload to Vercel Blob using server action
      const uploadResult = await uploadBlobAction(formData, user.uid)

      if (!uploadResult.success) {
        return {
          success: false,
          error: uploadResult.error || "Failed to upload image",
        }
      }

      // Update user document using domain hooks (Firebase Client SDK)
      const success = await updateUser(user.uid, {
        photoURL: uploadResult.url,
      })

      if (success) {
        toast({
          title: "Profile picture updated",
          description: "Your profile picture has been updated successfully.",
        })
        return {
          success: true,
          url: uploadResult.url,
        }
      } else {
        return {
          success: false,
          error: "Failed to update user profile",
        }
      }
    } catch (error) {
      console.error("Profile picture upload error:", error)
      return {
        success: false,
        error: "Something went wrong. Please try again.",
      }
    } finally {
      setUploading(false)
    }
  }

  const removeProfilePicture = async (currentPhotoURL?: string): Promise<ProfilePictureResult> => {
    if (!user?.uid) {
      return {
        success: false,
        error: "You must be logged in to remove a profile picture",
      }
    }

    setRemoving(true)
    try {
      // If there's a current photo URL and it's from our blob storage, delete it
      if (currentPhotoURL && currentPhotoURL.includes("blob.vercel-storage.com")) {
        try {
          await deleteBlobAction(currentPhotoURL)
        } catch (deleteError) {
          console.warn("Failed to delete old profile picture from blob storage:", deleteError)
          // Continue with the operation even if deletion fails
        }
      }

      // Update user document using domain hooks (Firebase Client SDK)
      const success = await updateUser(user.uid, {
        photoURL: null,
      })

      if (success) {
        toast({
          title: "Profile picture removed",
          description: "Your profile picture has been removed successfully.",
        })
        return {
          success: true,
        }
      } else {
        return {
          success: false,
          error: "Failed to update user profile",
        }
      }
    } catch (error) {
      console.error("Profile picture removal error:", error)
      return {
        success: false,
        error: "Something went wrong. Please try again.",
      }
    } finally {
      setRemoving(false)
    }
  }

  return {
    uploadProfilePicture,
    removeProfilePicture,
    uploading,
    removing,
  }
}
