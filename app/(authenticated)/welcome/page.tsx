"use client"

import { useEffect, useState } from "react"
import { useRouter, useSearchParams } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Skeleton } from "@/components/ui/skeleton"
import { Sparkles, ArrowRight, Eye, SkipForward } from "lucide-react"
import { useUser } from "@/lib/domains/auth/auth.hooks"
import { useNewUserExperience } from "@/lib/domains/user/user.hooks"
import { useNewUserTripSuggestions } from "@/lib/domains/ai-suggestions/ai-suggestions-new-user-trips.hooks"
import { OptimizedImage } from "@/components/optimized-image"
import { PageLoading } from "@/components/page-loading"
import { toast } from "@/components/ui/use-toast"

export default function WelcomePage() {
  const router = useRouter()
  const searchParams = useSearchParams()
  const user = useUser()
  const { isNewUser, checking, endNewUserExperience } = useNewUserExperience()
  const {
    suggestions,
    loading: suggestionsLoading,
    error: suggestionsError,
    showAiSuggestions,
    loadSuggestions,
  } = useNewUserTripSuggestions()

  const [selectedSuggestion, setSelectedSuggestion] = useState<number | null>(null)
  const [redirecting, setRedirecting] = useState(false)

  // Check if user should be on this page
  useEffect(() => {
    if (!checking && isNewUser === false) {
      // User is not new, redirect to dashboard
      router.push("/dashboard")
    }
  }, [isNewUser, checking, router])

  // Load suggestions when component mounts
  useEffect(() => {
    if (user && isNewUser === true) {
      loadSuggestions()
    }
  }, [user, isNewUser, loadSuggestions])

  const handleCreateTrip = async (suggestionIndex: number) => {
    if (!user) return

    setRedirecting(true)
    try {
      // End new user experience
      await endNewUserExperience()

      // Store selected suggestion index in sessionStorage for trip creation
      sessionStorage.setItem("selectedNewUserSuggestion", suggestionIndex.toString())

      // Redirect to squad creation with new user flag
      router.push("/squads/create?newUser=true")
    } catch (error) {
      console.error("Error handling create trip:", error)
      toast({
        title: "Error",
        description: "Something went wrong. Please try again.",
        variant: "destructive",
      })
      setRedirecting(false)
    }
  }

  const handleSkip = async () => {
    if (!user) return

    setRedirecting(true)
    try {
      // End new user experience
      await endNewUserExperience()

      toast({
        title: "Welcome to Togeda.ai!",
        description: "You can explore the app at your own pace.",
      })

      // Redirect to dashboard
      router.push("/dashboard")
    } catch (error) {
      console.error("Error handling skip:", error)
      toast({
        title: "Error",
        description: "Something went wrong. Please try again.",
        variant: "destructive",
      })
      setRedirecting(false)
    }
  }

  // Show loading while checking user status or redirecting
  if (checking || isNewUser === false || redirecting) {
    return <PageLoading message="Loading..." />
  }

  // Show error if user is not authenticated
  if (!user) {
    return <PageLoading message="Authenticating..." />
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-teal-50 to-yellow-50 flex items-center justify-center p-4">
      <div className="w-full max-w-4xl mx-auto">
        <div className="text-center mb-8">
          <div className="flex items-center justify-center mb-4">
            <Sparkles className="h-8 w-8 text-teal-600 mr-2" />
            <h1 className="text-4xl font-bold text-gray-900">Welcome to Togeda.ai!</h1>
          </div>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            As an introduction to the app, we'll let you experience first hand what Togeda.ai is. 
            As your first trip, here's what we suggest for your next adventure based on your preferences:
          </p>
        </div>

        {/* Trip Suggestions */}
        <div className="mb-8">
          {suggestionsLoading && (
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {[1, 2, 3].map((i) => (
                <Card key={i} className="overflow-hidden">
                  <Skeleton className="h-48 w-full" />
                  <CardHeader>
                    <Skeleton className="h-6 w-3/4" />
                    <Skeleton className="h-4 w-full" />
                  </CardHeader>
                  <CardContent>
                    <Skeleton className="h-10 w-full" />
                  </CardContent>
                </Card>
              ))}
            </div>
          )}

          {suggestionsError && (
            <Card className="text-center py-8">
              <CardContent>
                <p className="text-red-600 mb-4">{suggestionsError}</p>
                <Button onClick={() => loadSuggestions(true)} variant="outline">
                  Try Again
                </Button>
              </CardContent>
            </Card>
          )}

          {showAiSuggestions && (
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {suggestions.slice(0, 3).map((suggestion, index) => (
                <Card
                  key={index}
                  className={`overflow-hidden cursor-pointer transition-all duration-200 hover:shadow-lg ${
                    selectedSuggestion === index
                      ? "ring-2 ring-teal-500 shadow-lg"
                      : "hover:shadow-md"
                  }`}
                  onClick={() => setSelectedSuggestion(index)}
                >
                  <div className="relative h-48">
                    {suggestion.image ? (
                      <OptimizedImage
                        src={suggestion.image}
                        alt={suggestion.destination}
                        fill
                        className="object-cover"
                        attribution={suggestion.attribution}
                      />
                    ) : (
                      <div className="w-full h-full bg-gradient-to-br from-teal-100 to-yellow-100 flex items-center justify-center">
                        <span className="text-teal-600 text-lg font-medium">
                          {suggestion.destination}
                        </span>
                      </div>
                    )}
                  </div>

                  <CardHeader>
                    <CardTitle className="text-lg">{suggestion.destination}</CardTitle>
                    <CardDescription className="text-sm line-clamp-3">
                      {suggestion.description}
                    </CardDescription>
                  </CardHeader>

                  <CardContent>
                    <div className="flex flex-wrap gap-2 mb-4">
                      {suggestion.tags.slice(0, 3).map((tag, tagIndex) => (
                        <Badge key={tagIndex} variant="secondary" className="text-xs">
                          {tag}
                        </Badge>
                      ))}
                    </div>

                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium text-teal-600">
                        Budget: {suggestion.budget}
                      </span>
                      <Button
                        size="sm"
                        onClick={(e) => {
                          e.stopPropagation()
                          handleCreateTrip(index)
                        }}
                        className="bg-teal-600 hover:bg-teal-700"
                        disabled={redirecting}
                      >
                        Create This Trip
                        <ArrowRight className="h-4 w-4 ml-1" />
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </div>

        {/* Action Buttons */}
        <div className="text-center">
          <Button
            variant="outline"
            size="lg"
            onClick={handleSkip}
            disabled={redirecting}
            className="text-gray-600 hover:text-gray-800"
          >
            <SkipForward className="h-5 w-5 mr-2" />
            I would like to explore the app on my own
          </Button>
        </div>
      </div>
    </div>
  )
}
